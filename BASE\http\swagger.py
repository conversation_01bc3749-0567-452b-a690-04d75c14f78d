from litellm import acompletion
import json
import time
import traceback
import re
import uuid
import os
import asyncio
import requests
from typing import AsyncGenerator, Any
from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id
from BASE.services.swagger.generator import SwaggerSpecGenerator
from BASE.services.swagger.swagger_llm_service import generate_queries, generate_calls, structure_output
from BASE.actions.utils import SearchReferences
from ..utils.utils import log_memory_usage
from BASE.actions.swagger_search import _perform_swagger_search
from pathlib import Path
import yaml
from BASE.services.swagger.swagger_parser import _extract_endpoints
from BASE.services.swagger.resolver import SwaggerSpecRefsResolver

MESSAGE_DELIMITER = "<__!!__END__!!__>"


# Simple data structures to replace missing classes
class SwaggerRequestData:
    def __init__(self, data: dict):
        self.swagger_file_path = data.get("swagger_file_path", "")
        self.swagger_content = data.get("swagger_content", "")
        self.swagger_url = data.get("swagger_url", "")
        self.swagger_knowledgebase_id = data.get("swagger_knowledgebase_id", "")
        self.client_side_language = data.get("client_side_language", "")
        self.custom_instructions = data.get("custom_instructions", "")
        self.base_url = data.get("base_url", "")
        self.provider = data.get("provider", {})





@logger.catch()
async def process_endpoint(endpoint, client_language, custom_instructions):
    """Process a single endpoint and return the result."""
    start_time = time.time()
    endpoint_path = endpoint.get("path", "unknown")
    logger.info(f"Processing endpoint: {endpoint_path}")

    try:
        # Simple mock implementation - replace with actual processing logic
        payload = {
            "required_imports": ["import requests"],
            "code": f"# Generated code for {endpoint_path}\nprint('Hello, world!')",
            "endpoint": endpoint_path,
            "method": endpoint.get("method", "GET")
        }

        total_time = time.time() - start_time
        logger.debug(f"Endpoint processed in {total_time:.3f}s")
        return payload

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"Error processing endpoint {endpoint_path} after {total_time:.3f}s: {e}")
        raise

@logger.catch()
async def combine_imports(results):
    """Combine import statements from multiple results."""
    start_time = time.time()
    logger.debug("Combining import statements")

    try:
        unique_imports = set()
        for result in results:
            if "required_imports" in result:
                unique_imports.update(result["required_imports"])

        total_time = time.time() - start_time
        logger.debug(f"Combined {len(unique_imports)} unique imports in {total_time:.3f}s")
        return list(unique_imports)

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"Error combining imports after {total_time:.3f}s: {e}")
        return []

@logger.catch()
def prepare_spec_file(payload: SwaggerRequestData):
    """Prepare Swagger specification file from various sources."""
    start_time = time.time()
    logger.info("Preparing Swagger specification")

    try:
        generator = SwaggerSpecGenerator()
        endpoints = []

        if payload.swagger_file_path:
            logger.info(f"Generating endpoints from file: {payload.swagger_file_path}")
            endpoints = generator.generate_from_file(payload.swagger_file_path)
        elif payload.swagger_content:
            logger.info("Generating endpoints from content")
            try:
                swagger_dict = json.loads(payload.swagger_content)
                endpoints = generator.generate_from_content(swagger_dict)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON content provided: {e}")
                return None
        elif payload.swagger_url:
            logger.info(f"Generating endpoints from URL: {payload.swagger_url}")
            endpoints = generator.generate_from_url(payload.swagger_url)
        else:
            logger.error("No swagger source provided")
            return None

        total_time = time.time() - start_time
        logger.info(f"Swagger spec preparation completed - Endpoints: {len(endpoints)}, Time: {total_time:.3f}s")
        return endpoints

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"Error preparing Swagger spec after {total_time:.3f}s: {e}")
        return None

@logger.catch()
def format_response_object(response):
    """Format response object for consistent structure."""
    start_time = time.time()
    logger.debug("Formatting response object")

    try:
        formatted = {}
        for key in response.keys():
            formatted[key] = {
                "description": response[key]["description"],
                "schema": response[key].get("schema", {}),
                "headers": response[key].get("headers", {}),
            }

        total_time = time.time() - start_time
        logger.debug(f"Response formatting completed in {total_time:.3f}s")
        return formatted

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"Error formatting response after {total_time:.3f}s: {e}")
        return {}

@logger.catch()
def parse_prompt(input_text):
    """Parse prompt to extract structure and main content."""
    structure_pattern = re.compile(r"```structure\s*(.*?)\s*```", re.DOTALL)
    structure_match = structure_pattern.search(input_text)

    if structure_match:
        structure = structure_match.group(1).strip()
        prompt = input_text.replace(structure_match.group(0), "").strip()
    else:
        structure = None
        prompt = input_text.strip()

    return prompt, structure


@logger.catch()
async def handle_swagger_spec_generation(payload: SwaggerRequestData):
    """Handle Swagger spec generation and yield streaming results."""
    start_time = time.time()
    logger.info("Starting Swagger spec generation")

    has_yielded = False

    logger.info(f"Payload: {json.dumps(payload.__dict__, indent=2)}")

    try:
        generator = SwaggerSpecGenerator()
        endpoints = []

        # Get endpoints based on the provided input method
        content_start = time.time()
        if payload.swagger_file_path:
            logger.info(f"Generating endpoints from file: {payload.swagger_file_path}")
            endpoints = generator.generate_from_file(payload.swagger_file_path)
        elif payload.swagger_content:
            logger.info("Generating endpoints from content")
            try:
                file_name = f"swagger_temp_{uuid.uuid4()}.json"
                with open(file_name, "w+", encoding="utf-8") as f:
                    f.write(payload.swagger_content)
                endpoints = generator.generate_from_file(file_name)
                os.remove(file_name)
            except json.JSONDecodeError:
                logger.error("Invalid JSON content provided")
                has_yielded = True
                yield json.dumps({
                    "index": 0,
                    "type": "error",
                    "message": "Invalid JSON content provided"
                }) + MESSAGE_DELIMITER
                return
        elif payload.swagger_url:
            logger.info(f"Generating endpoints from URL: {payload.swagger_url}")
            endpoints = generator.generate_from_url(payload.swagger_url)
        else:
            logger.error("No swagger source provided")
            has_yielded = True
            yield json.dumps({
                "index": 0,
                "type": "error",
                "message": "No swagger source provided"
            }) + MESSAGE_DELIMITER
            return

        content_time = time.time() - content_start
        logger.info(f"Content processing completed in {content_time:.3f}s")
        logger.info(f"Found {len(endpoints)} endpoints to process")

        if not endpoints:
            logger.warning("No endpoints found in swagger specification")
            has_yielded = True
            yield json.dumps({
                "index": 0,
                "type": "message",
                "message": "No endpoints found in swagger specification"
            }) + MESSAGE_DELIMITER
            return

        # Process each endpoint
        processing_start = time.time()
        results = []

        for i, endpoint in enumerate(endpoints):
            try:
                # Yield endpoint start signal
                has_yielded = True
                yield json.dumps({
                    "index": i,
                    "type": "endpoint_start",
                    "endpoint": endpoint.get("path", "unknown"),
                    "method": endpoint.get("method", "GET")
                }) + MESSAGE_DELIMITER

                result = await process_endpoint(
                    endpoint,
                    payload.client_side_language,
                    payload.custom_instructions
                )

                # Yield endpoint success signal
                yield json.dumps({
                    "index": i,
                    "type": "endpoint_success",
                    "endpoint": endpoint.get("path", "unknown"),
                    "result": result
                }) + MESSAGE_DELIMITER

                if result:
                    results.append(result)

                # Small delay to prevent overwhelming the client
                await asyncio.sleep(0.001)

            except Exception as e:
                logger.error(f"Error processing endpoint {i}: {e}")
                yield json.dumps({
                    "index": i,
                    "type": "endpoint_error",
                    "endpoint": endpoint.get("path", "unknown"),
                    "error": str(e)
                }) + MESSAGE_DELIMITER
                continue

        processing_time = time.time() - processing_start
        logger.info(f"Endpoint processing completed - Success: {len(results)}, Failed: {len(endpoints) - len(results)}, Time: {processing_time:.3f}s")

        # Combine all the imports
        imports = await combine_imports(results)
        yield json.dumps({
            "index": 0,
            "type": "imports",
            "imports": imports
        }) + MESSAGE_DELIMITER

        # Final success message
        yield json.dumps({
            "index": 0,
            "type": "success",
            "message": f"Successfully processed {len(results)} endpoints",
            "total_endpoints": len(endpoints),
            "successful_endpoints": len(results)
        }) + MESSAGE_DELIMITER

        total_time = time.time() - start_time
        logger.info(f"Swagger spec generation completed in {total_time:.3f}s")

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"Error processing swagger request after {total_time:.3f}s: {e}")
        if not has_yielded:
            yield json.dumps({
                "index": 0,
                "type": "error",
                "message": f"Error: {str(e)}"
            }) + MESSAGE_DELIMITER

@logger.catch()
async def handle_swagger_prompt_generation(payload: SwaggerRequestData):
    """Handle Swagger prompt generation with LLM-powered query and call generation."""
    start_time = time.time()
    logger.info("Starting Swagger prompt generation")

    has_yielded = False

    try:
        user_prompt, structure_prompt = parse_prompt(payload.custom_instructions)
        kbid = payload.swagger_knowledgebase_id
        logger.info(f"Processing prompt for KB: {kbid}")


        # Step 1: Generate search queries using LLM
        has_yielded = True
        yield json.dumps({
            "index": 0,
            "type": "message",
            "message": "Generating search queries..."
        }) + MESSAGE_DELIMITER

        queries = await generate_queries(user_prompt, num_prompts=5)

        yield json.dumps({
            "index": 0,
            "type": "queries_generated",
            "queries": queries
        }) + MESSAGE_DELIMITER

        # Step 2: Perform searches using the generated queries
        yield json.dumps({
            "index": 0,
            "type": "message",
            "message": "Searching for relevant endpoints..."
        }) + MESSAGE_DELIMITER

        search_results = []
        for query in queries:
            logger.debug(f"Performing search with query: {query}")
            result = await _perform_swagger_search(query=query, index_name=kbid)
            search_results.extend(result)

        # Remove duplicates based on endpoint path
        final_results = []
        seen_paths = set()
        for res in search_results:
            if isinstance(res, dict) and "metadata" in res and "endpoint" in res["metadata"]:
                path = res["metadata"]["endpoint"].get("path", "")
                if path and path not in seen_paths:
                    final_results.append(res)
                    seen_paths.add(path)

        # Create search references
        search_references = SearchReferences(request_id="")
        for res in final_results:
            search_references.add_search_result(
                path=res["metadata"]["endpoint"]["path"],
                type="docs",
                content=json.dumps(res["metadata"]["endpoint"]),
                name=res["metadata"]["endpoint"]["path"],
            )

        yield json.dumps({
            "index": 0,
            "type": "references",
            "references": search_references.get_search_result()
        }) + MESSAGE_DELIMITER

        # Step 3: Get spec endpoints for context
        spec_endpoints = prepare_spec_file(payload) or []

        # Step 4: Generate API calls using LLM
        yield json.dumps({
            "index": 0,
            "type": "message",
            "message": "Generating API call sequence..."
        }) + MESSAGE_DELIMITER

        calls = await generate_calls(user_prompt, final_results, spec_endpoints)

        # Step 5: Process calls into response format
        response_json = {"name": "Swagger API", "calls": []}

        for call in calls:
            path = call.get("path", "")
            spec_data = next(
                (item for item in final_results
                 if item.get("metadata", {}).get("endpoint", {}).get("path") == path),
                None,
            )

            if spec_data:
                endpoint_data = spec_data["metadata"]["endpoint"]
                response_json["calls"].append({
                    "name": endpoint_data.get("operation_id", call.get("description", "Unknown")),
                    "sequence": call.get("sequence", 1),
                    "endpoint": path,
                    "method": call.get("method", endpoint_data.get("method", "GET")),
                    "params": endpoint_data.get("parameters", []),
                    "response": format_response_object(endpoint_data.get("responses", {})),
                    "description": call.get("description", "")
                })
            else:
                # Fallback for calls without matching spec data
                response_json["calls"].append({
                    "name": call.get("description", "Unknown"),
                    "sequence": call.get("sequence", 1),
                    "endpoint": path,
                    "method": call.get("method", "GET"),
                    "params": call.get("parameters", []),
                    "response": {},
                    "description": call.get("description", "")
                })

        # Step 6: Apply structure if provided
        if structure_prompt:
            yield json.dumps({
                "index": 0,
                "type": "message",
                "message": "Applying custom structure..."
            }) + MESSAGE_DELIMITER

            response_json = await structure_output(structure_prompt, response_json)

        # Final response
        yield json.dumps({
            "index": 0,
            "type": "kb_response",
            "response": response_json
        }) + MESSAGE_DELIMITER

        total_time = time.time() - start_time
        logger.info(f"Swagger prompt generation completed in {total_time:.3f}s")

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"Error in Swagger prompt generation after {total_time:.3f}s: {e}")
        if not has_yielded:
            yield json.dumps({
                "index": 0,
                "type": "error",
                "message": f"Error: {str(e)}"
            }) + MESSAGE_DELIMITER


@logger.catch()
async def swagger_stream(data: dict):
    """
    HTTP streaming endpoint for swagger generation functionality.
    Uses JSON streaming with MESSAGE_DELIMITER following the chat.py pattern.

    Args:
        data: Dictionary containing swagger request data
        session_id: Session identifier for tracking
    """
    start_time = time.time()

    logger.info("Starting HTTP swagger stream request")

    has_yielded = False

    try:
        # Parse the request data
        payload = SwaggerRequestData(data)

        # Route to appropriate handler based on knowledge base ID

        yield json.dumps({
            "index": 0,
            "type": "start",
            "message": "Processing swagger request..."
        }) + MESSAGE_DELIMITER

        if payload.swagger_knowledgebase_id == "":
            logger.info("Handling Swagger spec generation")
            async for event in handle_swagger_spec_generation(payload):
                has_yielded = True
                yield event
        else:
            logger.info("Handling Swagger prompt generation")
            async for event in handle_swagger_prompt_generation(payload):
                has_yielded = True
                yield event

        # Ensure we always yield something
        if not has_yielded:
            yield json.dumps({
                "index": 0,
                "type": "message",
                "message": "No response generated"
            }) + MESSAGE_DELIMITER

        # Final completion message
        yield json.dumps({
            "index": 0,
            "type": "end",
            "message": "Swagger processing completed"
        }) + MESSAGE_DELIMITER

        total_time = time.time() - start_time
        log_memory_usage("http_swagger_complete")


        logger.info(f"HTTP swagger stream completed successfully - Total time: {total_time:.2f}s")

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"Error in HTTP swagger stream after {total_time:.2f}s: {e}")
        logger.error(f"HTTP swagger error traceback: {traceback.format_exc()}")
        log_memory_usage("http_swagger_error")

        # Send error event using the MESSAGE_DELIMITER pattern
        yield json.dumps({
            "index": 0,
            "type": "error",
            "message": f"Error: {str(e)}"
        }) + MESSAGE_DELIMITER



@logger.catch()
async def swagger_list(data: dict):
    """List Swagger endpoints from various sources"""

    base_uri = "http://your-api-base-url"
    content = None

    # Log initial request data
    logger.info(f"swagger list data : {json.dumps(data, indent=2)}")
    logger.debug("Starting swagger list processing")

    try:
        if data.get("type") == "file":
            # Enhanced logging for file processing
            logger.info(f"Loading Swagger from file: {data.get('value')}")
            logger.debug(f"Attempting to read file at path: {data.get('value')}")
            
            file_path = Path(data.get('value'))
            if not file_path.exists():
                logger.error(f"File not found: {data.get('value')}")
                raise FileNotFoundError(f"Swagger file not found: {data.get('value')}")
            
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                logger.info(f"Found file. Size: {file_size/1024:.2f}KB")

            logger.debug(f"Swagger file size: {file_size/1024:.2f}KB")
            logger.info("Reading file contents...")

            with open(data.get('value'), "r", encoding="utf-8") as file:
                content = json.loads(file.read())
                logger.info("Successfully loaded file contents")

        elif data.get("type") == "url":
            logger.info(f"Loading Swagger from URL: {data.get('value')}")
            response = requests.get(
                url=data.get('value'),
                headers={"Content-Type": "application/json"},
            )

            logger.info(f"Response of url swagger: {response.json()}")

            if response.status_code == 200:
                content = response.json()
                logger.info("Successfully loaded URL contents")
            else:
                logger.error(
                    f"HTTP error fetching Swagger spec: {response.status_code}"
                )
                return {"error": f"HTTP error: {response.status_code}"}

        elif data.get("type") == "content":
            logger.info("Loading Swagger from provided content")
            try:
                content = json.loads(data.get('value'))
                logger.info("Successfully parsed JSON content")
            except Exception as e:
                logger.warning("JSON parsing failed, attempting YAML parsing")
                try:
                    # the content is in yaml format. convert it to json
                    content = yaml.safe_load(data.get('value'))
                    content = json.loads(json.dumps(content))
                    logger.info("Successfully parsed YAML content")
                except Exception as e:
                    logger.error(f"Error loading Swagger content: {e}")
                    return {"error": f"Error loading Swagger content: {e}"}

        else:
            logger.error(f"Invalid request type: {data.get('type')}")
            return {"error": "Invalid type. Must be 'file', 'url', or 'content'"}

        # Process the content
        processing_start = time.time()
        result = None

        if not data.get('full_specs', False):
            # Return simple endpoints list
            logger.info("Generating simple endpoints list")
            result = _extract_endpoints(swagger_data=content)
            logger.info(f"Extracted {len(result)} endpoints")

        else:
            # Generate full endpoint specifications
            logger.info("Generating full endpoint specifications")
            swagger_spec_resolver = SwaggerSpecRefsResolver()

            resolution_start = time.time()
            resolved_spec = swagger_spec_resolver.resolve_references(
                swagger_dict=content, base_uri=base_uri
            )
            resolution_time = time.time() - resolution_start
            logger.info(f"Reference resolution completed in {resolution_time:.3f}s")

            spec_generation_start = time.time()
            spec_list = swagger_spec_resolver.generate_endpoint_specs(resolved_spec)
            spec_generation_time = time.time() - spec_generation_start
            logger.info(f"Spec generation completed in {spec_generation_time:.3f}s")

            specs = []
            for spec in spec_list:
                specs.append(
                    {
                        "path": spec["path"],
                        "method": spec["method"],
                        "spec": spec["spec"],
                    }
                )
            result = specs
            logger.info(f"Generated full specs for {len(specs)} endpoints")

        processing_time = time.time() - processing_start

        logger.info(
            f"Swagger list request completed - "
            f"Type: {data.get('type')}, Full specs: {data.get('full_specs', False)}, "
            f"Processing time: {processing_time:.3f}s, "
        )

        return result

    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error: {str(e)}")
        return {"error": "Invalid JSON format"}

    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return {"error": str(e)}
