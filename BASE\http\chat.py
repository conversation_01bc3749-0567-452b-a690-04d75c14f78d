from litellm import acompletion
import json
from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id
import time
import traceback
from ..utils.utils import log_memory_usage
import os
from prompts import PROMPTS
from ..tools.tools_list import get as get_tools_list
from ..tools.execute_tool import execute_tool_call
from BASE.services.tokenizer import truncate_text_by_tokens
from BASE.actions.file_context import process_file_context
from BASE.services.llm_config import llm_config
import httpx
from constants import SSL_CONTEXT, cloud
import requests
from ipc import IPC

ipc_ = IPC.connect()


MESSAGE_DELIMITER = "<__!!__END__!!__>"


@logger.catch()
async def get_uuid():
    async with httpx.AsyncClient(verify=SSL_CONTEXT) as client:
        response = await client.get(f"{cloud}/uuid")
        result = response.json()
        return result.get("uuid", "")


@logger.catch()
def extract_last_user_message_content(payload_messages: list) -> str:
    """Extract the last user message content from payload messages."""
    for message in reversed(payload_messages):
        # Check if message is a user message
        if (isinstance(message, dict) and message.get("role") == "user") or (
            hasattr(message, "role") and message.role == "user"
        ):
            # Get content from either dict or object
            return (
                message.get("content", "")
                if isinstance(message, dict)
                else getattr(message, "content", "")
            )
    return ""


@logger.catch()
async def generate_followup_questions(
    last_user_message: str, current_content: str
) -> list:
    """Generate follow-up questions based on the last user message and conversation history."""
    try:
        logger.info(
            f"Generating follow-up questions for message: {last_user_message[:100]}..."
        )

        # Get system prompt for follow-up generation
        SYSTEM_PROMPT = await PROMPTS.get("followup_questions")

        # Prepare conversation context
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {
                "role": "user",
                "content": f"Context of our conversation:\n{current_content}\n\nUser's message: {last_user_message}",
            },
        ]

        llm_ = llm_config("chat_followup")

        # Generate questions
        response = await acompletion(
            base_url=llm_.get("base_url", "https://backend.v3.codemateai.dev/v1"),
            api_key=llm_.get("api_key", ""),
            model=llm_.get("model", "gpt-4.1-mini"),
            messages=messages,
            temperature=0.7,
            stream=False,
        )

        # Parse XML response
        if hasattr(response, "choices") and len(response.choices) > 0:
            content = response.choices[0].message.content

            # Extract questions from XML format
            questions = []
            if "<questions>" in content and "</questions>" in content:
                xml_content = content.split("<questions>")[1].split("</questions>")[0]
                question_tags = xml_content.split("<question>")

                for tag in question_tags[1:]:  # Skip first empty split
                    if "</question>" in tag:
                        question = tag.split("</question>")[0].strip()
                        if question:
                            questions.append(question)

                logger.info(f"Generated {len(questions)} follow-up questions")
                return questions[:5]  # Return up to 5 questions
            else:
                logger.warning("Response did not contain proper XML format")

        return []

    except Exception as e:
        logger.error(f"Error generating follow-up questions: {e}")
        logger.error(traceback.format_exc())
        return []


@logger.catch()
async def send_chat_history_to_cloud(
    conversation_messages: list[dict],
    session_id: str,
    assistant_response: str = "",
    conversation_id: str = "",
    mode: str = "",
    followup_questions: list[str] = [],
) -> None:
    """
    Send conversation messages to the cloud /chat/history endpoint.

    Args:
        conversation_messages: List of conversation messages from the current chat session
        session_id: Session identifier for the X-Session header
    """
    try:
        if not session_id:
            logger.warning(
                "No session ID provided, skipping chat history sync to cloud"
            )
            return

        if not conversation_messages:
            logger.warning("No conversation messages to send to cloud")
            return

        if not conversation_id:
            logger.warning(
                "No conversation ID provided, skipping chat history sync to cloud"
            )
            conversation_id = await get_uuid()

        if not mode:
            logger.warning("No mode provided, skipping chat history sync to cloud")
            mode = "NORMAL"

        # Create a deep copy to avoid modifying the original history_conv
        import copy

        messages_to_send = copy.deepcopy(conversation_messages)
        if len(followup_questions) > 0:
            messages_to_send.append({"role": "assistant", "content": assistant_response, "followups": followup_questions})
        else:
            messages_to_send.append({"role": "assistant", "content": assistant_response})
        

        # For now, just log the messages since cloud integration is commented out
        logger.info(
            f"Would send chat history to cloud with {len(messages_to_send)} messages"
        )
        logger.info(f"Conversation messages: {json.dumps(messages_to_send, indent=2)}")

        # Prepare the payload with conversation messages
        payload = {
            "conversation_id": conversation_id,
            "messages": messages_to_send,
            "mode": mode,
        }

        # Prepare headers with session identifier
        headers = {"X-Session": session_id, "Content-Type": "application/json"}
        logger.info(
            f"Sending chat history to cloud with conversation ID: {conversation_id}"
        )
        async with httpx.AsyncClient(
            verify=SSL_CONTEXT, timeout=30.0  # 30 second timeout
        ) as client:
            response = await client.post(
                f"{cloud}/history/update", json=payload, headers=headers
            )

            status = response.json().get("status", "")
            if status == "success":
                logger.info(f"Successfully sent chat history to cloud")
            else:
                logger.warning(
                    f"Failed to send chat history to cloud. Status: {status}"
                )

    except Exception as e:
        logger.error(f"Error sending chat history to cloud: {e}")
        logger.error(f"Chat history sync error traceback: {traceback.format_exc()}")
        # Don't raise the exception to avoid disrupting the main chat flow


@logger.catch()
async def process_context(ctx: dict, last_user_message: str) -> tuple[str, str]:
    """Process individual context and return replacement and additional content"""
    replacement_content = ""
    additional_content = ""

    logger.info(f"Processing context: {json.dumps(ctx, indent=2)}")

    if ctx["type"] == "file":
        # Use 'id' field as the file path if 'path' is not available
        file_path = ctx.get("path") or ctx.get("id", "")
        content = process_file_context(file_path)
        # logger.info(f"File content: {content}...")

        content = truncate_text_by_tokens(content)

        # logger.info(f"Truncated file content: {content[:200]}...")

        # Extract file name from path for cleaner display
        file_name = os.path.basename(file_path)

        # Create XML-style context marker with content included
        replacement_content = f"<cm:context>\n    <cm:context:name>{ctx.get('name', file_name)}</cm:context:name>\n    <cm:context:type>file</cm:context:type>\n    <cm:context:id>{ctx.get('id', file_path)}</cm:context:id>\n    <cm:context:content>{content}</cm:context:content>\n</cm:context>"

    elif ctx["type"] == "swagger":
        from .utils import process_swagger_context

        replacement_content, additional_content = await process_swagger_context(
            ctx, last_user_message
        )

    return replacement_content, additional_content


async def chat_stream(
    messages: list,
    call_for: str = "chat",
    session_id: str = "",
    is_web_search: bool = False,
    mode: str = "NORMAL",
    conversation_id: str = "",
    provide_followups: bool = False,
):
    """
    Handle chat streaming requests.

    llm_ :: Dictionary containing LLM configuration. => {
        "base_url": str,  # Base URL for the LLM API
        "api_key": str,  # API key for authentication | Default => session_id for CodeMate Cloud Calls.
        "model": str,  # Model name to use
    }
    messages :: List of messages in the chat.
    messages format :: {
        "role": str, # system, user, assistant, tool_call
        "content": str | dict # Content of the message, can be a string or a dict containing string and image base64,
        "context": list | None # Optional, list of contexts to use for the message
    }
    tools :: List of tools available for the LLM to use.
    """

    has_yielded = False
    llm_ = llm_config(call_for)
    logger.info(f"llm config: {json.dumps(llm_, indent=2)}")

    if not conversation_id or conversation_id == "":
        logger.warning("No conversation ID provided, generating new one")
        conversation_id = await get_uuid()

    try:
        # Create history_conv BEFORE processing contexts (to preserve original context arrays)
        # Use deep copy to ensure context arrays are preserved when we modify sequence_messages
        import copy

        history_conv = copy.deepcopy(messages)
        logger.info(f"history_conv: {json.dumps(history_conv, indent=2)}")

        SYSTEM_PROMPT = await PROMPTS.get(call_for)
        sequence_messages = [{"role": "system", "content": SYSTEM_PROMPT}]
        sequence_messages.extend(messages)

        # logger.info(f"[stream] Sequence messages: {sequence_messages}")

        contexts = []
        last_user_message = extract_last_user_message_content(messages)

        # Only process context for the LAST user message (most recent one)
        # Historical messages already have their context processed from previous requests
        last_user_message_index = -1
        for i in range(len(sequence_messages) - 1, -1, -1):
            if sequence_messages[i]["role"] == "user":
                last_user_message_index = i
                break

        if last_user_message_index != -1:
            message = sequence_messages[last_user_message_index]
            if "context" in message:
                additional_content_parts = []

                # Find the corresponding message in history_conv to synchronize content changes
                # The last user message in sequence_messages corresponds to the last user message in history_conv
                history_last_user_index = -1
                for i in range(len(history_conv) - 1, -1, -1):
                    if history_conv[i]["role"] == "user":
                        history_last_user_index = i
                        break

                history_message = (
                    history_conv[history_last_user_index]
                    if history_last_user_index != -1
                    else None
                )

                for ctx in message["context"]:
                    logger.info(f"Processing context item: {json.dumps(ctx, indent=2)}")
                    replacement, additional = await process_context(
                        ctx, last_user_message
                    )
                    logger.info(f"Generated replacement content length: {len(replacement) if replacement else 0} characters")

                    if replacement:
                        # Use a safer approach: find the specific context block by ID and replace it
                        # This avoids regex escaping issues with complex file paths
                        ctx_id = ctx.get('id', '')

                        # Look for the context block with this specific ID
                        # Use a simple string search approach that's more reliable
                        start_marker = f'<cm:context:id>{ctx_id}</cm:context:id>'

                        logger.debug(f"Looking for context marker: {start_marker}")
                        logger.debug(f"Message content length: {len(message['content'])} characters")

                        if start_marker in message["content"]:
                            # Find the start and end of this context block
                            start_pos = message["content"].find('<cm:context>')
                            while start_pos != -1:
                                # Find the end of this context block
                                end_pos = message["content"].find('</cm:context>', start_pos)
                                if end_pos != -1:
                                    # Extract the context block
                                    context_block = message["content"][start_pos:end_pos + len('</cm:context>')]

                                    # Check if this block contains our target ID
                                    if start_marker in context_block:
                                        # Replace this specific context block
                                        message["content"] = (
                                            message["content"][:start_pos] +
                                            replacement +
                                            message["content"][end_pos + len('</cm:context>'):]
                                        )
                                        logger.info(
                                            f"Replaced context marker with replacement content for context: {ctx}"
                                        )
                                        logger.info(f"Updated message content length: {len(message['content'])} characters")

                                        # Apply the same replacement to history_conv (for cloud sync)
                                        if history_message and start_marker in history_message["content"]:
                                            hist_start_pos = history_message["content"].find('<cm:context>')
                                            while hist_start_pos != -1:
                                                hist_end_pos = history_message["content"].find('</cm:context>', hist_start_pos)
                                                if hist_end_pos != -1:
                                                    hist_context_block = history_message["content"][hist_start_pos:hist_end_pos + len('</cm:context>')]
                                                    if start_marker in hist_context_block:
                                                        history_message["content"] = (
                                                            history_message["content"][:hist_start_pos] +
                                                            replacement +
                                                            history_message["content"][hist_end_pos + len('</cm:context>'):]
                                                        )
                                                        logger.info(
                                                            f"Synchronized content replacement in history_conv for context: {ctx}"
                                                        )
                                                        break
                                                hist_start_pos = history_message["content"].find('<cm:context>', hist_start_pos + 1)
                                        break

                                start_pos = message["content"].find('<cm:context>', start_pos + 1)
                        else:
                            logger.warning(
                                f"Context marker with ID {ctx_id} not found in message content"
                            )
                            logger.warning(f"Message content preview: {message['content'][:500]}...")

                    if additional:
                        additional_content_parts.append(additional)

                    contexts.append(ctx)

                # Add additional content if any
                if additional_content_parts:
                    additional_content_str = "\n\n" + "\n".join(
                        additional_content_parts
                    )
                    # Add to sequence_messages (for LLM)
                    message["content"] += additional_content_str
                    # Add to history_conv (for cloud sync)
                    if history_message:
                        history_message["content"] += additional_content_str

                # Remove context from the message that will be sent to LLM (but keep it in history_conv)
                del message["context"]

        logger.info(f"[stream] Contexts: {contexts}")

        tools = get_tools_list(contexts, is_web_search, mode)

        logger.info(f"[stream] Tools: {tools}")

        iteration = 0

        yield json.dumps({
                "index": 0,
                "type": "start",
                "message": "Conversation started"
        }) + MESSAGE_DELIMITER

        yield json.dumps({
                "index": 1,
                "type": "conversation_id",
                "message": conversation_id
        }) + MESSAGE_DELIMITER

        while True:
            iteration += 1
            # For custom endpoints, we need to prefix the model with openai/ to tell litellm to use OpenAI format

            response = await acompletion(
                base_url=llm_.get("base_url", "https://backend.v3.codemateai.dev/v1"),
                api_key=llm_.get("api_key", ""),
                model=llm_.get("model", "gpt-4.1-mini"),
                messages=sequence_messages,
                tools=tools if len(tools) != 0 else None,
                tool_choice="auto" if len(tools) != 0 else None,
                temperature=0.4,
                stream=True,
            )

            logger.info(f"sequence_messages: {json.dumps(sequence_messages, indent=2)}")

            chunks = []
            tool_calls = {}
            current_content = ""
            index = 2

            logger.info(f"[stream] Starting response stream")

            logger.info(f"[stream] Sending conversation ID: {conversation_id}")

            

            

            async for chunk in response:
                try:
                    if chunk.choices[0].delta.tool_calls:
                        for tool_call in chunk.choices[0].delta.tool_calls:
                            tool_call_index = tool_call.index
                            if tool_call_index not in tool_calls:
                                tool_calls[tool_call_index] = {
                                    "name": "",
                                    "args": "",
                                    "id": "",
                                }

                            if tool_call.id:
                                tool_calls[tool_call_index][
                                    "name"
                                ] += tool_call.function.name

                            if tool_call.function.arguments:
                                tool_calls[tool_call_index][
                                    "args"
                                ] += tool_call.function.arguments

                    if chunk.choices[0].delta.content:
                        current_content += chunk.choices[0].delta.content
                        has_yielded = True
                        yield json.dumps(
                            {
                                "index": index,
                                "type": "message",
                                "message": chunk.choices[0].delta.content,
                            }
                        ) + MESSAGE_DELIMITER

                except Exception as chunk_error:
                    logger.warning(
                        f"Error processing chunk {index}: {str(chunk_error)}"
                    )
                    continue
                finally:
                    index += 1

            if current_content.strip() and not tool_calls:
                logger.info(
                    f"[stream] Conversation completed after {iteration} iterations"
                )
                break

            if tool_calls:
                logger.info(f"[stream] Processing {len(tool_calls)} tool calls")

                # Add assistant message with tool calls to sequence
                assistant_message = {
                    "role": "assistant",
                    "content": current_content,
                    "tool_calls": [],
                }

                tool_results = []

                # Prepare all tool calls data for the new event
                all_tool_calls = []
                for tool_call_index, func_data in tool_calls.items():
                    if func_data["name"] and func_data["args"]:
                        tool_call_id = func_data["id"] or f"call_{tool_call_index}"
                        all_tool_calls.append(
                            {
                                "id": tool_call_id,
                                "name": func_data["name"],
                                "arguments": func_data["args"],
                                "index": tool_call_index,
                            }
                        )

                # Send the new tool_calls event with all tool calls
                if all_tool_calls:
                    has_yielded = True
                    yield json.dumps(
                        {"type": "tool_calls", "tool_calls": all_tool_calls}
                    ) + MESSAGE_DELIMITER

                for tool_call_index, func_data in tool_calls.items():
                    if func_data["name"] and func_data["args"]:
                        tool_call_id = func_data["id"] or f"call_{tool_call_index}"

                        # Yield tool call start signal
                        has_yielded = True
                        yield json.dumps(
                            {
                                "index": tool_call_index,
                                "type": "tool_call_start",
                                "tool_call": {
                                    "id": tool_call_id,
                                    "name": func_data["name"],
                                    "arguments": func_data["args"],
                                },
                            }
                        ) + MESSAGE_DELIMITER

                        # Add to assistant message
                        assistant_message["tool_calls"].append(
                            {
                                "id": tool_call_id,
                                "type": "function",
                                "function": {
                                    "name": func_data["name"],
                                    "arguments": func_data["args"],
                                },
                            }
                        )

                        # Execute the tool call
                        tool_call_data = {
                            "id": tool_call_id,
                            "function": {
                                "name": func_data["name"],
                                "arguments": func_data["args"],
                            },
                        }

                        try:
                            tool_result, _ = await execute_tool_call(tool_call_data)

                            # Parse the JSON string content back to object for frontend
                            try:
                                parsed_result = json.loads(tool_result["content"])
                            except (json.JSONDecodeError, TypeError):
                                # If parsing fails, keep as string
                                parsed_result = tool_result["content"]

                            # Yield tool call completion signal
                            yield json.dumps(
                                {
                                    "index": tool_call_index,
                                    "type": "tool_call_complete",
                                    "tool_call": {
                                        "id": tool_call_id,
                                        "name": func_data["name"],
                                        "result": parsed_result,
                                    },
                                }
                            ) + MESSAGE_DELIMITER

                            # Add tool result to conversation
                            tool_results.append(
                                {
                                    "role": "tool",
                                    "tool_call_id": tool_result["tool_call_id"],
                                    "content": tool_result["content"],
                                    "name": func_data["name"],
                                }
                            )

                        except Exception as tool_error:
                            logger.error(f"Error executing tool call: {tool_error}")

                            error_content = {
                                "error": str(tool_error),
                                "status": "error",
                            }

                            # Yield tool call error signal
                            yield json.dumps(
                                {
                                    "index": tool_call_index,
                                    "type": "tool_call_complete",
                                    "tool_call": {
                                        "id": tool_call_id,
                                        "name": func_data["name"],
                                        "result": error_content,
                                        "metadata": {
                                            "status": "error",
                                            "timestamp": time.time(),
                                            "error": str(tool_error),
                                        },
                                    },
                                }
                            ) + MESSAGE_DELIMITER

                            tool_results.append(
                                {
                                    "role": "tool",
                                    "tool_call_id": tool_call_id,
                                    "content": json.dumps(error_content),
                                    "name": func_data["name"],
                                }
                            )

                # Add assistant message and tool results to sequence
                sequence_messages.append(assistant_message)
                sequence_messages.extend(tool_results)
                history_conv.append(assistant_message)
                history_conv.extend(tool_results)

                # Continue the conversation loop to get LLM response to tool results
                continue

            # If no content and no tool calls, break to avoid infinite loop
            if not current_content.strip() and not tool_calls:
                logger.info(f"[stream] No content or tool calls, ending conversation")
                break

        # Ensure we always yield something
        if not has_yielded:
            yield json.dumps(
                {"index": 0, "type": "message", "message": "No response generated"}
            ) + MESSAGE_DELIMITER


        yield json.dumps(
            {"index": index, "type": "end", "message": "End of conversation"}
        ) + MESSAGE_DELIMITER

        

        # Handle follow-up functionality after successful response completion
        followup_questions = []
        logger.info(f"Provide followups: {provide_followups}")

        if provide_followups:
            try:
                # Extract last user message content
                last_user_message_content = extract_last_user_message_content(messages)

                if last_user_message_content:
                    logger.info(
                        f"Processing follow-ups for last user message: {last_user_message_content[:100]}..."
                    )

                    # Generate follow-up questions
                    followup_questions = await generate_followup_questions(
                        last_user_message_content, current_content
                    )

                    if followup_questions:
                        yield json.dumps(
                            {
                                "index": 0,
                                "type": "follow_ups",
                                "message": followup_questions,
                            }
                        ) + MESSAGE_DELIMITER

                        logger.info(
                            f"Successfully processed {len(followup_questions)} follow-up questions"
                        )
                    else:
                        logger.info("No follow-up questions generated")
                else:
                    logger.warning("No last user message content found for follow-ups")
            except Exception as followup_error:
                logger.error(f"Error processing follow-ups: {followup_error}")
                # Don't let follow-up errors affect the main response

        try:
            logger.info(f"history conv :{json.dumps(history_conv, indent=2)}")

            await send_chat_history_to_cloud(
                history_conv, session_id, current_content, conversation_id, mode, followup_questions
            )
        except Exception as cloud_error:
            logger.error(f"Error sending chat history to cloud: {cloud_error}")

            yield json.dumps(
                {"index": 0, "type": "end", "message": "End of conversation"}
            ) + MESSAGE_DELIMITER

            

    except Exception as e:
        logger.error(f"Error in chat_stream: {str(e)}")
        yield json.dumps(
            {"index": 0, "type": "error", "message": f"Error: {str(e)}"}
        ) + MESSAGE_DELIMITER





async def delete_chat_history(conversation_id: str):
    """Delete chat history from database"""
    session_id = ipc_.get("current_session", None)
    if not session_id:
        logger.error("No session provided for chat history delete")
        return {"status": "error", "message": "No session provided"}
    try:
        response = requests.delete(
            f"{cloud}/history/{conversation_id}",
            headers={"Content-Type": "application/json", "X-Session": session_id},
            )
        return response.json()
    except Exception as e:
        logger.error(f"Error deleting chat history: {e}")
        return {"status": "error", "message": f"Error deleting chat history: {e}"}




async def get_chat_history(conversation_id: str):
    """Get chat history from database"""
    session_id = ipc_.get("current_session", None)
    if not session_id:
        logger.error("No session provided for chat history fetch")
        return {"status": "error", "message": "No session provided"}
    try:
        response = requests.post(
            f"{cloud}/history/{conversation_id}",
            json={"conversation_id": conversation_id},
            headers={"Content-Type": "application/json", "X-Session": session_id},
            )
        return response.json()
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        return {"status": "error", "message": f"Error getting chat history: {e}"}



 
async def get_all_chat_history():
    """Get all chat history from database"""
    session_id = ipc_.get("current_session", None)
    if not session_id:
        logger.error("No session provided for chat history fetch")
        return {"status": "error", "message": "No session provided"}
    
    try:
        response = requests.post(
            f"{cloud}/history",
            headers={"Content-Type": "application/json", "X-Session": session_id},
            )
        return response.json()
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        return {"status": "error", "message": f"Error getting chat history: {e}"}
