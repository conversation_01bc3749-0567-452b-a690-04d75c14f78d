import json
from logger.log import logger
from BASE.http.swagger import swagger_list
from BASE.services.knowledge_bases import get_knowledge_base_metadata
from BASE.services.swagger.swagger_llm_service import generate_queries
from BASE.actions.swagger_search import _perform_swagger_search


def extract_last_user_message_content(payload_messages: list) -> str:
    """Extract the last user message content from payload messages."""
    try:
        # Find the last message with role 'user'
        for message in reversed(payload_messages):
            if message.get("role") == "user":
                return message.get("content", "")
        return ""
    except Exception as e:
        logger.error(f"Error extracting last user message: {e}")
        return ""


async def process_swagger_context(ctx: dict,last_user_message: str) -> tuple[str, str]:
    """Process Swagger context and return replacement and additional content

    Args:
        ctx (dict): Context dictionary containing Swagger information

    Returns:
        tuple[str, str]: (replacement_content, additional_content)
    """

    logger.info(f"Processing swagger context: {json.dumps(ctx, indent=2)}")
    replacement_content = ""

    query = last_user_message

    try:
        # Get swagger content from knowledge base or direct content
        swagger_content = ""

        # Check if we have a kbid (knowledge base ID) to fetch content from
        kbid = ctx.get("kbid")
        if kbid:
            # Fetch swagger content from knowledge base
            query_list = await generate_queries(query)
            search_results = []
            for q_index, query in enumerate(query_list):
                logger.info(f"Query {q_index}: {query}")

                search_result = await _perform_swagger_search(query=query, index_name=kbid)

                search_results.extend(search_result)

                logger.info(f"Search results: {json.dumps(search_results, indent=2)}")

            final_search_results_map = {}
            for end in search_results:
                final_search_results_map[end.get("path", "")] = end
                logger.info(f"Final search results: {json.dumps(final_search_results_map, indent=2)}")
            final_search_results = list(final_search_results_map.values())
            final_search_results.sort(key=lambda x: x.get("path", ""))

            swagger_content = json.dumps(final_search_results, indent=2)

        # Create XML-style context marker with content included (similar to file processing)
        if swagger_content:
            # Truncate content if too large to avoid token limits
            swagger_content = truncate_swagger_content(swagger_content, max_tokens=15000)

            replacement_content = f"<cm:context>\n    <cm:context:name>{ctx.get('name', 'Swagger API')}</cm:context:name>\n    <cm:context:type>swagger</cm:context:type>\n    <cm:context:id>{ctx.get('id', '')}</cm:context:id>\n    <cm:context:content>{swagger_content}</cm:context:content>\n</cm:context>"
        else:
            # Fallback if no content found
            replacement_content = f"<cm:context>\n    <cm:context:name>{ctx.get('name', 'Swagger API')}</cm:context:name>\n    <cm:context:type>swagger</cm:context:type>\n    <cm:context:id>{ctx.get('id', '')}</cm:context:id>\n    <cm:context:content>No swagger content available</cm:context:content>\n</cm:context>"

    except Exception as e:
        logger.error(f"Error processing Swagger context: {e}")
        replacement_content = f"<cm:context>\n    <cm:context:name>{ctx.get('name', 'Swagger API')}</cm:context:name>\n    <cm:context:type>swagger</cm:context:type>\n    <cm:context:id>{ctx.get('id', '')}</cm:context:id>\n    <cm:context:content>Error processing swagger: {str(e)}</cm:context:content>\n</cm:context>"

    return replacement_content, ""  # Return tuple with empty additional_content


# def _get_swagger_content_from_kb(kbid: str) -> str:
#     """Fetch swagger content from knowledge base

#     Args:
#         kbid (str): Knowledge base ID

#     Returns:
#         str: Swagger content as string
#     """
#     try:
#         logger.info(f"Fetching swagger content from KB: {kbid}")

#         # Import here to avoid circular imports


#         # Get knowledge base metadata to understand the swagger source
#         kb_metadata = get_knowledge_base_metadata(kbid)
#         if not kb_metadata:
#             return f"Knowledge base {kbid} not found"

#         # Extract swagger metadata
#         swagger_metadata = kb_metadata.get("metadata", {})
#         source_type = swagger_metadata.get("source_type", "")
#         source_value = swagger_metadata.get("source_value", "")
#         endpoints = swagger_metadata.get("endpoints", [])

#         # Format the swagger content based on available data
#         content = f"Swagger API Documentation (KB: {kbid})\n\n"

#         if source_type and source_value:
#             content += f"Source: {source_type} - {source_value}\n\n"

#         if endpoints:
#             content += f"Available Endpoints ({len(endpoints)} total):\n\n"
#             for endpoint in endpoints[:20]:  # Limit to first 20 endpoints
#                 method = endpoint.get("method", "").upper()
#                 path = endpoint.get("path", "")
#                 summary = endpoint.get("summary", "")
#                 content += f"{method} {path}"
#                 if summary:
#                     content += f" - {summary}"
#                 content += "\n"

#             if len(endpoints) > 20:
#                 content += f"\n... and {len(endpoints) - 20} more endpoints\n"
#         else:
#             content += "No endpoint information available\n"

#         return content

#     except Exception as e:
#         logger.error(f"Error fetching swagger from KB {kbid}: {e}")
#         return f"Error fetching swagger content: {str(e)}"


# def _format_swagger_content_endpoints(endpoints: list) -> str:
#     """Format simple endpoints list as content string (not for display)

#     Args:
#         endpoints (list): List of endpoint dictionaries

#     Returns:
#         str: Formatted endpoints content
#     """
#     if not endpoints:
#         return "No endpoints found"

#     content = "Swagger API Endpoints:\n\n"

#     for endpoint in endpoints:
#         method = endpoint.get("method", "").upper()
#         path = endpoint.get("path", "")
#         summary = endpoint.get("summary", "")
#         operation_id = endpoint.get("operation_id", "")
#         description = endpoint.get("description", "")
#         parameters = endpoint.get("parameters", [])

#         content += f"{method} {path}\n"
#         if summary:
#             content += f"Summary: {summary}\n"
#         if description:
#             content += f"Description: {description}\n"
#         if operation_id:
#             content += f"Operation ID: {operation_id}\n"
#         if parameters:
#             content += f"Parameters: {len(parameters)} parameter(s)\n"
#             for param in parameters[:3]:  # Show first 3 parameters
#                 param_name = param.get("name", "")
#                 param_type = param.get("type", param.get("schema", {}).get("type", ""))
#                 param_required = param.get("required", False)
#                 content += f"  - {param_name} ({param_type})" + (" [required]" if param_required else "") + "\n"
#             if len(parameters) > 3:
#                 content += f"  ... and {len(parameters) - 3} more parameters\n"
#         content += "\n"

#     return content


# def _format_swagger_content_full_specs(specs: list) -> str:
#     """Format full endpoint specifications as content string

#     Args:
#         specs (list): List of full endpoint specifications

#     Returns:
#         str: Formatted specifications content
#     """
#     if not specs:
#         return "No specifications found"

#     content = "Swagger API Full Specifications:\n\n"

#     for spec in specs:
#         method = spec.get("method", "").upper()
#         path = spec.get("path", "")
#         spec_data = spec.get("spec", {})

#         content += f"--- {method} {path} ---\n"
#         content += json.dumps(spec_data, indent=2)
#         content += "\n\n"

#     return content


# def _process_swagger_sync(data: dict) -> dict:
#     """Synchronous fallback for Swagger processing when async context is not available
    
#     Args:
#         data (dict): Swagger data to process
        
#     Returns:
#         dict: Processed result or error
#     """
#     try:
#         # Import here to avoid circular imports
#         from BASE.services.swagger.swagger_parser import _extract_endpoints
        
#         request_type = data.get("type")
#         request_value = data.get("value")
#         full_specs = data.get("full_specs", False)
        
#         content = None
        
#         if request_type == "content":
#             try:
#                 content = json.loads(request_value)
#             except json.JSONDecodeError:
#                 import yaml
#                 try:
#                     content = yaml.safe_load(request_value)
#                 except Exception as e:
#                     return {"error": f"Invalid content format: {e}"}
#         else:
#             return {"error": "Only 'content' type supported in sync mode"}
        
#         if not full_specs:
#             return _extract_endpoints(swagger_data=content)
#         else:
#             # For full specs, we need the async version
#             return {"error": "Full specs require async processing"}
            
#     except Exception as e:
#         return {"error": str(e)}


def format_swagger_endpoints(endpoints: list) -> str:
    """Format simple endpoints list for chat context
    
    Args:
        endpoints (list): List of endpoint dictionaries
        
    Returns:
        str: Formatted endpoints string
    """
    if not endpoints:
        return "\n=====\nSwagger API: No endpoints found\n=====\n"
    
    formatted = "\n=====\nSwagger API Endpoints:\n"
    
    for endpoint in endpoints:
        method = endpoint.get("method", "").upper()
        path = endpoint.get("path", "")
        summary = endpoint.get("summary", "")
        operation_id = endpoint.get("operation_id", "")
        
        formatted += f"\n{method} {path}"
        if summary:
            formatted += f" - {summary}"
        if operation_id:
            formatted += f" (ID: {operation_id})"
        formatted += "\n"
    
    formatted += "=====\n"
    return formatted


def format_swagger_full_specs(specs: list) -> str:
    """Format full endpoint specifications for chat context
    
    Args:
        specs (list): List of full endpoint specifications
        
    Returns:
        str: Formatted specifications string
    """
    if not specs:
        return "\n=====\nSwagger API: No specifications found\n=====\n"
    
    formatted = "\n=====\nSwagger API Full Specifications:\n"
    
    for spec in specs:
        method = spec.get("method", "").upper()
        path = spec.get("path", "")
        spec_data = spec.get("spec", {})
        
        formatted += f"\n--- {method} {path} ---\n"
        formatted += json.dumps(spec_data, indent=2)
        formatted += "\n"
    
    formatted += "=====\n"
    return formatted


def estimate_swagger_token_count(content: str) -> int:
    """Estimate token count for Swagger content
    
    Args:
        content (str): Content to estimate
        
    Returns:
        int: Estimated token count
    """
    return len(content.split()) * 1.3  # Rough approximation


def truncate_swagger_content(content: str, max_tokens: int = 15000) -> str:
    """Truncate Swagger content if it exceeds token limit
    
    Args:
        content (str): Content to potentially truncate
        max_tokens (int): Maximum token limit
        
    Returns:
        str: Potentially truncated content
    """
    estimated_tokens = estimate_swagger_token_count(content)
    
    if estimated_tokens <= max_tokens:
        return content
    
    # Calculate approximate character limit
    char_limit = int((max_tokens / estimated_tokens) * len(content) * 0.9)  # 90% safety margin
    
    if char_limit < len(content):
        truncated = content[:char_limit]
        truncated += "\n\n[Content truncated due to size limit]"
        logger.warning(f"Swagger content truncated from {len(content)} to {len(truncated)} characters")
        return truncated
    
    return content
