from itertools import chain
import os
import uuid
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Any, Callable, Coroutine

from .utils import make_chunks_from_file


async def clone_repository_simple(repo_url: str, target_path: str) -> bool:
    """Simple repository cloning function."""
    print(f"Repository will be cloned to: {target_path}")
    try:
        clone_command = ["git", "clone", repo_url, target_path]
        print(f"Executing git clone command: {' '.join(clone_command)}")

        clone_start = time.time()
        process = await asyncio.create_subprocess_exec(
            *clone_command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await process.communicate()
        clone_time = time.time() - clone_start

        if process.returncode == 0:
            print(f"Git repository cloned successfully in {clone_time:.2f} seconds to {target_path}")
            return True
        else:
            error_output = stderr.decode() if stderr else "No error output"
            print(f"Error cloning repository. Return code: {process.returncode}")
            print(f"Command error: {error_output}")

            if "git" in error_output.lower() and "not found" in error_output.lower():
                raise RuntimeError("Git CLI not found. Please install Git.")
            else:
                raise RuntimeError(f"Error cloning repository: {error_output}")

    except Exception as e:
        print(f"Error cloning repository: {e}")
        raise RuntimeError(f"Error cloning repository: {e}")


def discover_files_simple(repo_dir: str) -> list[str]:
    """Simple file discovery function."""
    file_discovery_start = time.time()
    files = []

    # Simple recursive file discovery
    for root, dirs, filenames in os.walk(repo_dir):
        for filename in filenames:
            file_path = os.path.join(root, filename)
            files.append(file_path)

    file_discovery_time = time.time() - file_discovery_start
    print(f"Found {len(files)} files in cloned repository (discovery took {file_discovery_time:.2f}s)")

    # Calculate repository size
    repo_size = 0
    for file_path in files:
        try:
            repo_size += os.path.getsize(file_path)
        except OSError:
            pass
    print(f"Repository size: {repo_size / 1024 / 1024:.2f} MB")

    return files


async def process_github_chunks(
    repo_url: str,
    target_dir: str,
    progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
) -> list[dict]:

    """Process GitHub repository and return chunks as simple dictionaries."""
    print("Processing Github type knowledge base")

    # Create unique target path
    target_path = os.path.join(target_dir, str(uuid.uuid4()))

    # Clone repository
    await clone_repository_simple(repo_url, target_path)

    # Discover files
    files = discover_files_simple(target_path)

    print(f"Found {len(files)} files for github processing")

    # Log file type distribution
    file_extensions = {}
    total_size = 0
    for file_path in files:
        ext = os.path.splitext(file_path)[1] or "no_extension"
        file_extensions[ext] = file_extensions.get(ext, 0) + 1
        try:
            total_size += os.path.getsize(file_path)
        except OSError:
            pass

    print(f"File distribution: {dict(sorted(file_extensions.items()))}")
    print(f"Total github size: {total_size / 1024 / 1024:.2f} MB")

    try:
        chunking_phase_start = time.time()
        print("Starting chunk creation phase")

        MAX_WORKERS = int(os.cpu_count() * 0.4)
        chunks_by_file = {}
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            chunk_futures = {}
            # Dispatch all the futures
            dispatch_start = time.time()
            print("Dispatching chunk creation tasks to thread pool")
            for file in files:
                chunk_futures[file] = executor.submit(make_chunks_from_file, file)
            dispatch_time = time.time() - dispatch_start
            print(f"Dispatched {len(chunk_futures)} chunk creation tasks in {dispatch_time:.2f}s")

            # Collect the results
            collection_start = time.time()
            completed_files = 0
            for i, (file, chunk_future) in enumerate(chunk_futures.items()):
                file_start = time.time()
                # Wait for the future to complete
                chunks_by_file[file] = chunk_future.result()
                file_time = time.time() - file_start
                completed_files += 1

                file_chunk_count = len(chunks_by_file[file])
                print(f"Completed chunking for file {completed_files}/{len(chunk_futures)}: {file} "
                      f"({file_chunk_count} chunks, took {file_time:.2f}s)")

                progress = i / len(chunk_futures) * 100
                print(f"Progress: {progress:.2f}%")
                if progress_callback:
                    await progress_callback(progress)

            collection_time = time.time() - collection_start
            print(f"Chunk collection completed in {collection_time:.2f} seconds")

        total_chunks = len(list(chain(*chunks_by_file.values())))
        chunking_phase_time = time.time() - chunking_phase_start

        print(f"Chunk creation completed. Total chunks created: {total_chunks} in {chunking_phase_time:.2f}s")

        if total_chunks == 0:
            print("No chunks found after processing all files")
            return []

        # Log chunking statistics
        files_with_chunks = sum(
            1 for chunks_list in chunks_by_file.values() if len(chunks_list) > 0
        )
        files_without_chunks = len(chunks_by_file) - files_with_chunks
        avg_chunks_per_file = (
            total_chunks / files_with_chunks if files_with_chunks > 0 else 0
        )

        print(f"Chunking stats - Files with chunks: {files_with_chunks}, "
              f"Files without chunks: {files_without_chunks}, "
              f"Avg chunks per file: {avg_chunks_per_file:.1f}")

        return list(chain(*chunks_by_file.values()))

    except Exception as e:
        print(f"Error processing files: {e}")
        return []
