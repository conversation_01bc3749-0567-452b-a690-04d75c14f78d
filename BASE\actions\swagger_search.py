import json
from typing import Any
from BASE.embeddings.embeddings import generate_embeddings_cloud
from BASE.vdb.qdrant import get_qdrant_client
import requests
import constants
from BASE.services.swagger.swagger_llm_service import swagger_query_api, generate_queries

async def _perform_swagger_search(
    query: str,
    index_name: str,
    limit: int = 20,
) -> list[dict[str, Any]]:
    qc = get_qdrant_client()

    try:
        query_embeddings = await generate_embeddings_cloud(False, query)
        results = await qc.search(
            collection_name=index_name,
            query_vector=("vectors", query_embeddings),
            limit=limit,
            with_payload=True,
        )

        # name -> swagger endpoint spec
        results_map: dict[str, dict[str, Any]] = {}
        for result in results:
            metadata = result.payload
            results_map[metadata.get("name", "")] = metadata.get("additional_metadata", {})

        api_results = await swagger_query_api(query, results)
        filtered_results = list(map(lambda x: results_map[x], api_results))
        return filtered_results
    except Exception as e:
        raise


async def process_swagger_search(
    query: str, tool_id: str, kbid: str, search_references
):
    """Process swagger search actions."""
    try:
        # Generate multiple search queries using LLM
        query_list = await generate_queries(query)

        # Perform search with all generated queries
        all_search_results = []
        for generated_query in query_list:
            search_results = await _perform_swagger_search(query=generated_query, index_name=kbid)
            all_search_results.extend(search_results)

        # Remove duplicates based on name/path while preserving order
        seen_names = set()
        unique_results = []
        for result in all_search_results:
            result_name = result.get("name", "")
            if result_name not in seen_names:
                seen_names.add(result_name)
                unique_results.append(result)

        # Add search results to search_references (maintaining existing functionality)
        for result in unique_results:
            search_references.add_search_result(
                type="file",
                name=result.get("name", ""),
                path=result.get("name", ""),
                content=json.dumps(result.get("additional_metadata", {})),
            )

        # Maintain existing return format
        final_content = json.dumps(unique_results)

        status = "error"

        if final_content:
            status = "success"

        return {"status": status, "content": final_content}, search_references

    except Exception as e:
        print(f"Error in process_swagger_search: {e}")
        raise
